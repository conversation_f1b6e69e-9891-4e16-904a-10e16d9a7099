using SSMClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SSMClient
{
    public abstract partial class SSMClientBase
    {
        /// <summary>
        /// 建档/办卡 TJ1003
        /// </summary>
        /// <param name="inParam">建档/办卡信息</param>
        /// <param name="outParam">人员档案信息</param>
        /// <returns>0成功，-1失败，-2超时</returns>
        public virtual int CreatCardInfo(DTO.CreatCardInfo_In inParam, ref DTO.CreatCardInfo_Out outParam)
        {
            return 0;
        }


        /// <summary>
        ///  电子健康卡获取会话
        /// </summary>
        public virtual int GetToken(ref DTO.GetToken_Out outParam)
        {
            return 0;
        }
        /// <summary>
        ///  通过卡号和卡类型查身份
        /// </summary>
        /// <param name="inParam">卡号,卡类型</param>
        /// <param name="outParam">人员档案信息</param>
        /// <returns>0成功，-1失败，-2超时</returns>
        public virtual int GetPatientInfo(DTO.GetPatientInfo_In inParam, ref DTO.GetPatientInfo_Out outParam)
        {
            return 0;
        }


        /// <summary>
        /// 电子健康卡注册
        /// </summary>
        /// <param name="inParam"></param>
        /// <param name="outParam"></param>
        /// <returns></returns>
        public virtual int GetCardRegistration(DTO.CardRegistration_In inParam, ref DTO.CardRegistration_Out outParam)
        {
            return 0;
        }

        /// <summary>
        /// 电子健康卡 - 获取主索引信息
        /// </summary>
        /// <param name="inParam"></param>
        /// <param name="outParam"></param>
        /// <returns></returns>
        public virtual int GetRecognizeIdentifier(DTO.RecognizeIdentifier_In inParam, ref DTO.RecognizeIdentifier_Out outParam)
        {
            return 0;
        }

        /// <summary>
        /// 电子健康卡识别
        /// </summary>
        /// <param name="inParam"></param>
        /// <param name="outParam"></param>
        /// <returns></returns>
        public virtual int GetQueryQrcode(DTO.QueryQrcode_In inParam, ref DTO.QueryQrcode_Out outParam)
        {
            return 0;
        }

        /// <summary>
        /// 10.4医保卡信息获取1101 交易 
        /// </summary>
        /// <param name="inParam"></param>
        /// <param name="outParam"></param>
        /// <returns></returns>
        public virtual int ReadCardInsur(DTO.MedicalInsuranceGetPatientInfo_In inParam, ref DTO.MedicalInsuranceGetPatientInfo_Out outParam)
        {
            return 0;
        }

        /// <summary>
        /// 医保退结算交易2206
        /// </summary>
        /// <param name="inParam"></param>
        /// <param name="outParam"></param>
        /// <returns></returns>
        public virtual int InsurRefund(DTO.InsurRefundIn inParam, ref DTO.InsurRefundOut outParam)
        {
            return 0;
        }
    }
}
